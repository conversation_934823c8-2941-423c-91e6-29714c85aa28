<?php

namespace Theme25;

use Theme25\Helper\DatabaseHelperTrait;
use Theme25\Helper\GeneralHelperTrait;

class Model {

    protected $registry;
    protected $originalModel = null;
    protected $originalModelClass = null;

    use GeneralHelperTrait;
    use DatabaseHelperTrait;
    

    public function __construct($registry, $load_original_model = false) {
        $this->registry = $registry;
        $this->getGeneralHelper();
        if($load_original_model) {
            // Автоматично зареждане на оригиналния модел, ако съществува
            $this->_loadOriginalModel();
        }
    }

    /**
     * Зарежда оригиналния OpenCart модел, ако съществува
     */
    protected function _loadOriginalModel() {
        // Получаване на текущия клас и определяне на пътя към оригиналния модел
        $currentClass = get_class($this);
        $route = $this->getRouteFromClass($currentClass);
        if ($route) {
            $originalModelPath = DIR_APPLICATION . 'model/' . $route . '.php';
            $originalModelClass = 'Model' . preg_replace('/[^a-zA-Z0-9]/', '', $route);
            if (file_exists($originalModelPath)) {
                require_once($originalModelPath);
                if (class_exists($originalModelClass)) {
                    $this->originalModel = new $originalModelClass($this->registry);
                    $this->originalModelClass = $originalModelClass;
                }
            }
        }
    }

    /**
     * Извлича route от името на класа
     */
    protected function getRouteFromClass($className) {
        // Премахване на namespace префикса
        $className = str_replace('Theme25\\Backend\\Model\\', '', $className);
        $className = str_replace('Theme25\\Frontend\\Model\\', '', $className);
        $className = str_replace('Theme25\\Model\\', '', $className);

        // Конвертиране от CamelCase към route формат
        $parts = explode('\\', $className);
        $route = '';

        foreach ($parts as $index => $part) {
            if ($index > 0) {
                $route .= '/';
            }

            // Конвертиране от CamelCase към lowercase с подчертавки
            $route .= strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $part));
        }

        return $route;
    }

    /**
     * Магически метод за извикване на методи, които не съществуват в текущия клас
     * Този метод заменя __call от AutoDatabaseTrait и добавя поддръжка за оригиналния модел
     */
    public function __call($method, $args) {

        F()->log->developer($method, __FILE__, __LINE__);

        // 1. Първо проверяваме дали методът започва с 'db' и правим автоматично превключване
        if (strpos($method, 'db') === 0 && $this->isAutoSwitchEnabled()) {
            $this->autoSwitchDatabase();
        }

        // 2. Проверяваме дали методът съществува в generalHelper (от AutoDatabaseTrait логиката)
        if (!empty($this->generalHelper) || method_exists($this, 'getGeneralHelper')) {
            if (empty($this->generalHelper)) {
                $this->generalHelper = $this->getGeneralHelper();
            }

            if ($this->generalHelper && is_callable([$this->generalHelper, $method])) {
                return call_user_func_array([$this->generalHelper, $method], $args);
            }
        }

        // 3. Проверяваме дали методът съществува в оригиналния OpenCart модел
        if ($this->originalModel && is_callable([$this->originalModel, $method])) {
            return call_user_func_array([$this->originalModel, $method], $args);
        }

        // 4. Проверяваме дали методът съществува в родителския клас (от AutoDatabaseTrait логиката)
        $parentClass = get_parent_class($this);
        if ($parentClass && is_callable([$parentClass, $method])) {
            return call_user_func_array(['parent', $method], $args);
        }

        // 5. Ако методът не съществува никъде, хвърляме информативна грешка
        $errorMessage = "Метод '{$method}' не съществува в " . get_class($this);
        if ($this->originalModelClass) {
            $errorMessage .= " или в {$this->originalModelClass}";
        }
        if (!empty($this->generalHelper)) {
            $errorMessage .= " или в GeneralHelper";
        }

        F()->log->developer($errorMessage, __FILE__, __LINE__);

        throw new \Exception($errorMessage);
    }

	public function __get($key) {
		return $this->registry->get($key);
	}

	public function __set($key, $value) {
		$this->registry->set($key, $value);
	}

    /**
     * Проверява дали оригиналният модел е зареден
     */
    public function hasOriginalModel() {
        return $this->originalModel !== null;
    }

    /**
     * Връща инстанцията на оригиналния модел
     */
    public function getOriginalModel() {
        return $this->originalModel;
    }

    /**
     * Връща името на класа на оригиналния модел
     */
    public function getOriginalModelClass() {
        return $this->originalModelClass;
    }

}